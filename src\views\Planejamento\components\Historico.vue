<template>
  <div class="tratamento-content">
    <div class="row">
      <div class="col-md-12">
        <!-- Botão para registrar histórico -->
        <div class="header-actions mb-3">
          <button
            class="btn btn-primary btn-sm"
            @click="abrirModalRegistrarHistorico"
            title="Registrar novo histórico"
          >
            <font-awesome-icon :icon="['fas', 'plus']" class="me-2" />
            Registrar Histórico
          </button>
        </div>

        <div v-if="isLoading" class="w-100 text-center py-3">
          <div class="spinner-border text-primary" role="status"></div>
          <p class="mt-2">Carregando histórico do paciente...</p>
        </div>

        <div v-else-if="!historicosExibidos.length" class="empty-state">
          <div class="empty-state-message">
            <div class="icon-wrapper">
              <font-awesome-icon :icon="['fas', 'history']" class="empty-state-icon" />
            </div>
            <p>Não há registros de histórico para este paciente.</p>
            <p class="text-muted small">Clique em "Registrar Histórico" para adicionar o primeiro registro.</p>
          </div>
        </div>

        <div v-else class="historico-timeline">
          <!-- Item futuro: O que fazer na próxima consulta -->
          <div v-if="proximaConsultaInfo" class="historico-item futuro-item">
            <div class="historico-badge futuro-badge">
              <font-awesome-icon :icon="['fas', 'arrow-right']" />
            </div>
            <div class="historico-panel futuro-panel">
              <div class="historico-heading">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="historico-title">
                    O que fazer na próxima consulta
                    <span class="futuro-indicator">Próximo</span>
                  </h6>
                </div>
                <p class="historico-date">
                  <small>Baseado na última consulta realizada</small>
                </p>
              </div>
              <div class="historico-body">
                <p>{{ proximaConsultaInfo }}</p>
              </div>
            </div>
          </div>

          <!-- Históricos normais -->
          <div v-for="(historico, index) in historicosExibidos" :key="index" class="historico-item">
            <div class="historico-badge" :class="getHistoricoBadgeClass(historico)">
              <font-awesome-icon :icon="['fas', getHistoricoIcon(historico)]" />
            </div>
            <div class="historico-panel" :class="getHistoricoPanelClass(historico)">
              <div class="historico-heading">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="historico-title">
                    {{ getHistoricoTitle(historico) }}
                  </h6>
                </div>
                <p class="historico-date">
                  <small>{{ $filters.dateDmy(historico.data) }} às {{ formatTime(historico.horario) }}</small>
                </p>
              </div>
              <div class="historico-body">
                <p>{{ historico.descricao }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para registrar histórico -->
    <RegistrarHistoricoModal ref="registrarHistoricoModal" @historico-salvo="recarregarHistoricos" />
  </div>
</template>

<style scoped>
.tratamento-content {
  padding: 20px;
  padding-top: 5px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px dashed #d1dce8;
  width: 100%;
}

.empty-state-message .icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f2ff, #d1e6ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-message .empty-state-icon {
  font-size: 1.5rem;
  color: #5a9bd5;
}

/* Estilos para o histórico principal */
.historico-timeline {
  position: relative;
  padding: 20px 0;
  max-width: 800px;
  margin: 0 auto;
}

.historico-timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 18px;
  width: 2px;
  background: linear-gradient(to bottom, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.6));
  border-radius: 1px;
}

.historico-item {
  position: relative;
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.historico-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  text-align: center;
  font-size: 0.9em;
  line-height: 36px;
  background-color: white;
  border: 2px solid #007bff;
  color: #007bff;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
}

.historico-panel {
  position: relative;
  margin-left: 60px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.historico-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1dce8;
}

.historico-panel:before {
  content: '';
  position: absolute;
  top: 10px;
  left: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #e9ecef;
}

.historico-panel:after {
  content: '';
  position: absolute;
  top: 10px;
  left: -9px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.historico-title {
  margin-top: 0;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 1rem;
}

.historico-date {
  color: #6c757d;
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.historico-body > p {
  margin-bottom: 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.5;
}



/* Estilos para o header com botão */
.header-actions {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;
}

.header-actions .btn {
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
}

.header-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Estilos para consultas */
.consulta-badge {
  border-color: #28a745;
  color: #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.consulta-panel {
  border-left: 3px solid #28a745;
}

.consulta-panel:before {
  border-right-color: #28a745;
}

/* Estilos para item futuro */
.futuro-item {
  position: relative;
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease-out;
}

.futuro-badge {
  border-color: #ffc107;
  color: #ffc107;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.futuro-panel {
  border-left: 3px solid #ffc107;
  background: linear-gradient(135deg, #fff9e6, #fffbf0);
  border: 1px solid #ffeaa7;
}

.futuro-panel:before {
  border-right-color: #ffeaa7;
}

.futuro-panel:after {
  border-right-color: #fff9e6;
}

.futuro-indicator {
  display: inline-block;
  font-size: 0.65rem;
  font-weight: 500;
  color: #fff;
  background-color: #ffc107;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  text-transform: uppercase;
  vertical-align: middle;
  letter-spacing: 0.5px;
}
</style>

<script>
import moment from 'moment';
import { getHistoricosPaciente } from "@/services/historicoPacienteService";
import { getConsultasByPaciente } from "@/services/consultasService";
import RegistrarHistoricoModal from "@/components/RegistrarHistoricoModal.vue";

export default {
  name: "Historico",
  components: {
    RegistrarHistoricoModal
  },
  props: {
    paciente: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isLoading: false,
      historicos: [],
      consultas: [],
      proximaConsultaInfo: null
    }
  },
  computed: {
    historicosExibidos() {
      // Combinar consultas e históricos
      const items = [];

      // Adicionar consultas como históricos
      this.consultas.forEach(consulta => {
        if (consulta.status === 'realizada') {
          items.push({
            id: `consulta_${consulta.id}`,
            data: consulta.horario.split(' ')[0], // Extrair apenas a data
            horario: consulta.horario.split(' ')[1] || '00:00:00', // Extrair apenas o horário
            descricao: `Consulta realizada - ${this.getCategoriaNome(consulta.categoria)}`,
            codigo_acao: 'consulta_realizada',
            tipo: 'consulta',
            consulta_id: consulta.id,
            categoria: consulta.categoria
          });
        }
      });

      // Adicionar históricos (manuais e de consulta)
      this.historicos.forEach(historico => {
        if (historico.codigo_acao === 'alteracao_consulta') {
          // Para históricos de consulta, processar as modificações
          if (historico.modificacoes) {
            try {
              let modificacoes = typeof historico.modificacoes === 'string'
                ? JSON.parse(historico.modificacoes)
                : historico.modificacoes;

              if (Array.isArray(modificacoes)) {
                modificacoes.forEach(mod => {
                  if (mod.descricao && mod.descricao.trim()) {
                    items.push({
                      id: `${historico.id}_${mod.titulo}`,
                      data: historico.data,
                      horario: historico.horario,
                      descricao: mod.descricao,
                      codigo_acao: mod.titulo === 'Histórico da consulta' ? 'historico_consulta' : 'orientacao_proxima_consulta',
                      tipo: 'historico_consulta',
                      titulo_modificacao: mod.titulo,
                      consulta_id: historico.consulta_id
                    });
                  }
                });
              }
            } catch (e) {
              console.error('Erro ao processar modificações:', e);
            }
          }
        } else {
          // Históricos manuais normais
          items.push({
            ...historico,
            tipo: 'historico_manual'
          });
        }
      });

      // Ordenar por data/horário (mais recente primeiro)
      return items.sort((a, b) => {
        const dateA = new Date(`${a.data} ${a.horario}`);
        const dateB = new Date(`${b.data} ${b.horario}`);
        return dateB - dateA;
      });
    }
  },
  methods: {
    formatTime(dateTime) {
      if (!dateTime) return '-';

      try {
        // Se for uma string de data completa, extrair apenas a parte da hora
        if (typeof dateTime === 'string') {
          // Verificar se é uma data completa (YYYY-MM-DD HH:MM:SS)
          if (dateTime.includes('T') || dateTime.includes(' ')) {
            const date = new Date(dateTime);
            if (!isNaN(date.getTime())) {
              return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            }
          }

          // Se for apenas um horário (HH:MM:SS)
          if (dateTime.includes(':')) {
            const timeParts = dateTime.split(':');
            if (timeParts.length >= 2) {
              return `${timeParts[0]}:${timeParts[1]}`;
            }
          }
        }

        // Se for um objeto Date
        if (dateTime instanceof Date) {
          return dateTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        // Tentar com moment como último recurso
        return moment(dateTime).format('HH:mm');
      } catch (error) {
        console.error('Erro ao formatar horário:', error);
        return '-';
      }
    },
    getHistoricoTitle(historico) {
      // Determinar o título com base no tipo e código de ação
      if (historico.tipo === 'consulta') {
        return `Consulta - ${this.getCategoriaNome(historico.categoria)}`;
      }

      if (historico.tipo === 'historico_consulta') {
        return historico.titulo_modificacao || 'Histórico da consulta';
      }

      switch (historico.codigo_acao) {
        case 'registro_manual':
          return 'Registro de histórico';
        case 'historico_consulta':
          return 'Histórico da consulta';
        case 'orientacao_proxima_consulta':
          return 'Orientações para próxima consulta';
        case 'alteracao_consulta':
          return 'Alterações na consulta';
        case 'inicio_tratamento':
          return 'Início do tratamento';
        case 'alteracao_aparelho':
          return 'Alteração de aparelho';
        case 'ajuste_aparelho':
          return 'Ajuste de aparelho';
        case 'remocao_aparelho':
          return 'Remoção de aparelho';
        default:
          return historico.codigo_acao ? historico.codigo_acao : 'Registro de histórico';
      }
    },
    getHistoricoIcon(historico) {
      if (historico.tipo === 'consulta') {
        return 'calendar-check';
      }

      if (historico.tipo === 'historico_consulta') {
        return historico.codigo_acao === 'orientacao_proxima_consulta' ? 'arrow-right' : 'clipboard-list';
      }

      switch (historico.codigo_acao) {
        case 'registro_manual':
          return 'edit';
        case 'historico_consulta':
          return 'clipboard-list';
        case 'orientacao_proxima_consulta':
          return 'arrow-right';
        case 'alteracao_consulta':
          return 'clipboard-list';
        case 'inicio_tratamento':
          return 'play-circle';
        case 'alteracao_aparelho':
        case 'ajuste_aparelho':
          return 'wrench';
        case 'remocao_aparelho':
          return 'minus-circle';
        default:
          return 'circle';
      }
    },
    getHistoricoBadgeClass(historico) {
      if (historico.tipo === 'consulta') {
        return 'consulta-badge';
      }
      if (historico.tipo === 'historico_consulta') {
        return 'historico-consulta-badge';
      }
      return '';
    },
    getHistoricoPanelClass(historico) {
      if (historico.tipo === 'consulta') {
        return 'consulta-panel';
      }
      if (historico.tipo === 'historico_consulta') {
        return 'historico-consulta-panel';
      }
      return '';
    },
    // Métodos para categorias (copiados do ConsultasTable)
    getCategoriaNome(categoria) {
      const categorias = {
        'acompanhamento': 'Acompanhamento/ativação',
        'primeira_consulta': 'Primeira consulta',
        'emergencia': 'Emergência',
        'montagem': 'Montagem',
        'remocao': 'Remoção',
        'replanejamento': 'Replanejamento',
        'pos_tratamento': 'Pós-tratamento'
      };
      return categorias[categoria] || categoria || '-';
    },
    abrirModalRegistrarHistorico() {
      this.$refs.registrarHistoricoModal.abrirModal(this.paciente.id);
    },
    async carregarHistoricos() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoading = true;
      try {
        // Carregar todos os históricos (manuais e de consulta)
        const responseHistoricos = await getHistoricosPaciente(this.paciente.id);
        if (responseHistoricos && Array.isArray(responseHistoricos)) {
          this.historicos = responseHistoricos;
        } else {
          this.historicos = [];
        }

        // Carregar consultas
        const responseConsultas = await getConsultasByPaciente(this.paciente.id);
        if (responseConsultas && Array.isArray(responseConsultas)) {
          this.consultas = responseConsultas;

          // Buscar informação da próxima consulta da última consulta realizada
          await this.buscarProximaConsultaInfo();
        } else {
          this.consultas = [];
        }
      } catch (error) {
        console.error('Erro ao buscar dados do paciente:', error);
        this.historicos = [];
        this.consultas = [];
      } finally {
        this.isLoading = false;
      }
    },
    async buscarProximaConsultaInfo() {
      // Buscar a orientação mais recente para próxima consulta nos históricos já carregados
      const orientacoesProximaConsulta = this.historicos
        .filter(h => h.codigo_acao === 'alteracao_consulta' && h.modificacoes)
        .map(h => {
          try {
            let modificacoes = typeof h.modificacoes === 'string'
              ? JSON.parse(h.modificacoes)
              : h.modificacoes;

            if (Array.isArray(modificacoes)) {
              const proximaConsulta = modificacoes.find(m => m.titulo === "O que fazer na próxima consulta");
              if (proximaConsulta && proximaConsulta.descricao && proximaConsulta.descricao.trim()) {
                return {
                  descricao: proximaConsulta.descricao,
                  data: h.data,
                  horario: h.horario
                };
              }
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
          }
          return null;
        })
        .filter(item => item !== null)
        .sort((a, b) => {
          const dateA = new Date(`${a.data} ${a.horario}`);
          const dateB = new Date(`${b.data} ${b.horario}`);
          return dateB - dateA; // Mais recente primeiro
        });

      if (orientacoesProximaConsulta.length > 0) {
        this.proximaConsultaInfo = orientacoesProximaConsulta[0].descricao;
      }
    },
    recarregarHistoricos() {
      this.carregarHistoricos();
    }
  },
  mounted() {
    this.carregarHistoricos();
  },
  watch: {
    'paciente.id': {
      handler(newVal) {
        if (newVal) {
          this.carregarHistoricos();
        }
      },
      immediate: true
    }
  }
};
</script>